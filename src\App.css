/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: #1a1a1a;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

#root {
  min-height: 100vh;
  background-color: #1a1a1a;
}

/* Main app container */
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #1a1a1a;
  color: #ffffff;
}

/* Header styles */
.app-header {
  text-align: center;
  margin-bottom: 30px;
}

.app-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffffff;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.app-subtitle {
  font-size: 1.1rem;
  color: #cccccc;
  margin: 0;
}

/* Player container */
.player-container {
  margin: 30px 0;
  border: 2px solid #333;
  border-radius: 12px;
  overflow: hidden;
  background-color: #000;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

/* Instructions section */
.instructions-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #2a2a2a;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.instructions-title {
  color: #ffffff;
  font-size: 1.3rem;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.instructions-text {
  color: #cccccc;
  line-height: 1.6;
  margin: 0 0 15px 0;
}

.instructions-list {
  color: #cccccc;
  margin: 15px 0;
  padding-left: 20px;
}

.instructions-list li {
  margin: 8px 0;
  line-height: 1.5;
}

.instructions-list code {
  background-color: #333;
  color: #4CAF50;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-left: 10px;
}

.status-success {
  background-color: #4CAF50;
  color: white;
}

.status-warning {
  background-color: #FF9800;
  color: white;
}

.status-error {
  background-color: #f44336;
  color: white;
}
