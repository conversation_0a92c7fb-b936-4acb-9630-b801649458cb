import { useEffect, useState } from 'react';

interface FormatSupport {
  format: string;
  mimeType: string;
  support: string;
  canPlay: boolean;
}

export const VideoFormatDebug: React.FC = () => {
  const [formatSupport, setFormatSupport] = useState<FormatSupport[]>([]);

  useEffect(() => {
    const video = document.createElement('video');
    
    const formats = [
      { format: 'MP4', mimeType: 'video/mp4' },
      { format: 'MP4 (H.264)', mimeType: 'video/mp4; codecs="avc1.42E01E"' },
      { format: 'WebM', mimeType: 'video/webm' },
      { format: 'WebM (VP8)', mimeType: 'video/webm; codecs="vp8"' },
      { format: 'WebM (VP9)', mimeType: 'video/webm; codecs="vp9"' },
      { format: 'MOV', mimeType: 'video/quicktime' },
      { format: 'OGG', mimeType: 'video/ogg' }
    ];

    const support = formats.map(({ format, mimeType }) => {
      const supportLevel = video.canPlayType(mimeType);
      return {
        format,
        mimeType,
        support: supportLevel || 'No support',
        canPlay: supportLevel !== ''
      };
    });

    setFormatSupport(support);
    console.log('Browser video format support:', support);
  }, []);

  return (
    <div style={{
      margin: '20px 0',
      padding: '15px',
      backgroundColor: '#2a2a2a',
      borderRadius: '8px',
      border: '1px solid #444'
    }}>
      <h4 style={{ color: '#ffffff', margin: '0 0 15px 0' }}>
        🔍 Browser Video Format Support
      </h4>
      
      <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
        {formatSupport.map((item, index) => (
          <div key={index} style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '4px 0',
            borderBottom: index < formatSupport.length - 1 ? '1px solid #444' : 'none'
          }}>
            <span style={{ color: '#cccccc' }}>{item.format}</span>
            <span style={{
              color: item.canPlay ? '#4CAF50' : '#f44336',
              fontWeight: 'bold'
            }}>
              {item.support}
            </span>
          </div>
        ))}
      </div>
      
      <div style={{
        marginTop: '10px',
        fontSize: '11px',
        color: '#888',
        fontStyle: 'italic'
      }}>
        Support levels: "probably" = full support, "maybe" = partial support, "" = no support
      </div>
    </div>
  );
};
