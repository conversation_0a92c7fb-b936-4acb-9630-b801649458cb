import { useState, useEffect } from 'react';

interface VideoClipData {
  src: string;
  durationInFrames: number;
  durationInSeconds: number;
  name: string;
  format: string;
  loaded: boolean;
}

const initialVideoClips = [
  { baseName: 'clip1', name: 'Clip 1' },
  { baseName: 'clip2', name: 'Clip 2' },
  { baseName: 'clip3', name: 'Clip 3' }
];

// Function to check if browser supports the video format
const canPlayFormat = (format: string): boolean => {
  const video = document.createElement('video');
  const mimeTypes = {
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'mov': 'video/quicktime'
  };

  const mimeType = mimeTypes[format as keyof typeof mimeTypes];
  if (!mimeType) return false;

  const canPlay = video.canPlayType(mimeType);
  return canPlay !== '';
};

// Function to find the first available video format
const findAvailableVideo = async (baseName: string): Promise<string | null> => {
  const formats = ['mp4', 'webm', 'mov'];

  for (const format of formats) {
    const src = `/videos/${baseName}.${format}`;

    // Check browser support first
    if (!canPlayFormat(format)) {
      console.warn(`Browser doesn't support ${format} format, skipping ${src}`);
      continue;
    }

    try {
      const response = await fetch(src, { method: 'HEAD' });
      if (response.ok) {
        console.log(`Found video: ${src}`);
        return src;
      }
    } catch (error) {
      console.error(`Error checking ${src}:`, error);
    }
  }

  console.warn(`No video found for ${baseName} in any supported format`);
  return null;
};

export const useVideoDurations = (fps: number = 30) => {
  const [videoClips, setVideoClips] = useState<VideoClipData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalDurationInFrames, setTotalDurationInFrames] = useState(900); // Default fallback

  const getVideoDuration = (src: string): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        const duration = video.duration;
        resolve(duration);
      };

      video.onerror = () => {
        reject(new Error(`Failed to load video: ${src}`));
      };

      video.src = src;
    });
  };

  useEffect(() => {
    const loadAllVideoDurations = async () => {
      setLoading(true);

      const loadedClips: VideoClipData[] = [];

      for (const clip of initialVideoClips) {
        const availableVideo = await findAvailableVideo(clip.baseName);

        if (availableVideo) {
          try {
            const durationInSeconds = await getVideoDuration(availableVideo);
            const durationInFrames = Math.round(durationInSeconds * fps);
            const format = availableVideo.split('.').pop() || 'unknown';

            loadedClips.push({
              src: availableVideo,
              durationInSeconds,
              durationInFrames,
              name: clip.name,
              format: format.toUpperCase(),
              loaded: true
            });
          } catch (error) {
            console.error(`Error loading ${availableVideo}:`, error);
            // Add fallback entry
            loadedClips.push({
              src: availableVideo,
              durationInSeconds: 10, // Fallback
              durationInFrames: 300, // Fallback
              name: clip.name,
              format: 'ERROR',
              loaded: false
            });
          }
        }
      }

      setVideoClips(loadedClips);
      const total = loadedClips.reduce((sum, clip) => sum + clip.durationInFrames, 0);
      setTotalDurationInFrames(total || 900); // Fallback to 900 if no videos
      setLoading(false);
    };

    loadAllVideoDurations();
  }, [fps]);

  return {
    videoClips,
    loading,
    totalDurationInFrames,
    totalDurationInSeconds: totalDurationInFrames / fps
  };
};
