import { Player } from '@remotion/player';
import { VideoStitching } from './remotion/VideoStitching';
import { useVideoDurations } from './hooks/useVideoDurations';
import { VideoFormatDebug } from './components/VideoFormatDebug';
import './App.css';

function App() {
  const { videoClips, loading, totalDurationInFrames, totalDurationInSeconds } = useVideoDurations(30);

  return (
    <div className="app-container">
      <header className="app-header">
        <h1 className="app-title">Remotion Video Editor</h1>
        <p className="app-subtitle">Phase 1: Basic Video Stitching Demo</p>
      </header>

      <div className="player-container">
        <Player
          component={VideoStitching}
          durationInFrames={totalDurationInFrames}
          compositionWidth={1920}
          compositionHeight={1080}
          fps={30}
          style={{
            width: '100%',
            height: 'auto',
            aspectRatio: '16/9'
          }}
          controls
          showVolumeControls
          clickToPlay
        />
      </div>

      <VideoFormatDebug />

      <div className="instructions-section">
        <h3 className="instructions-title">
          Video Information
          {loading ? (
            <span className="status-indicator status-warning">⏳ Loading...</span>
          ) : (
            <span className="status-indicator status-success">✓ Loaded</span>
          )}
        </h3>

        {loading ? (
          <p className="instructions-text">
            🎬 Analyzing video files to get actual durations...
          </p>
        ) : (
          <>
            <p className="instructions-text">
              <strong>Total Duration:</strong> {totalDurationInSeconds.toFixed(1)} seconds ({totalDurationInFrames} frames)
            </p>

            <div className="instructions-text">
              <strong>Individual Clips:</strong>
            </div>
            <ul className="instructions-list">
              {videoClips.map((clip, index) => (
                <li key={index}>
                  <code>{clip.src.split('/').pop()}</code> - {clip.name}
                  {clip.loaded ? (
                    <span style={{ color: '#4CAF50' }}>
                      {' '}({clip.durationInSeconds.toFixed(1)} seconds, {clip.format})
                    </span>
                  ) : (
                    <span style={{ color: '#f44336' }}> (Failed to load)</span>
                  )}
                </li>
              ))}
            </ul>

            <p className="instructions-text">
              The videos are automatically stitched together in sequence. Use the player controls to navigate through the composition.
            </p>
          </>
        )}
      </div>
    </div>
  );
}

export default App;
