# Sample Videos Directory

This directory should contain sample video files for testing the Remotion video editor.

## Supported Video Formats:

The editor now supports **multiple video formats** and will automatically detect the first available format for each clip:

### Priority Order:
1. **MP4** (`.mp4`) - Most compatible
2. **WebM** (`.webm`) - Web-optimized format
3. **MOV** (`.mov`) - QuickTime format

## Required Files for Phase 1 Demo:

Add video files with any of the supported formats:

- `clip1.mp4` OR `clip1.webm` OR `clip1.mov` - First video clip
- `clip2.mp4` OR `clip2.webm` OR `clip2.mov` - Second video clip
- `clip3.mp4` OR `clip3.webm` OR `clip3.mov` - Third video clip

## File Requirements:

- **Format**: MP4, WebM, or MOV
- **Duration**: Any duration (automatically detected)
- **Resolution**: Any resolution will work, but 1920x1080 is optimal
- **Content**: Any video content is fine for testing purposes

## How it Works:

1. **Format Detection**: The system checks for each clip in this order: MP4 → WebM → MOV
2. **Duration Detection**: Automatically reads the actual duration of each video file
3. **Automatic Stitching**: Videos are stitched together in sequence based on their actual durations

Example: If you have `clip1.webm` (8.5s), `clip2.mp4` (12.3s), and `clip3.mov` (6.7s), the total composition will be 27.5 seconds.

## Format Examples:

```
/public/videos/
├── clip1.mp4     ✅ Will use MP4
├── clip2.webm    ✅ Will use WebM (no MP4 found)
├── clip3.mov     ✅ Will use MOV (no MP4 or WebM found)
└── README.md
```

## Note:

- **Mix and match formats** - You can use different formats for different clips
- **Automatic fallback** - If MP4 isn't available, it tries WebM, then MOV
- **Real-time detection** - The interface shows which format was found for each clip
- **Error handling** - Clear messages if no supported format is found
