import { Sequence, useCurrent<PERSON>rame, useVideoConfig } from 'remotion';
import { VideoClip } from './VideoClip';
import { useEffect, useState } from 'react';

// Function to check if browser supports the video format
const canPlayFormat = (format: string): boolean => {
  const video = document.createElement('video');
  const mimeTypes = {
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'mov': 'video/quicktime'
  };

  const mimeType = mimeTypes[format as keyof typeof mimeTypes];
  if (!mimeType) return false;

  const canPlay = video.canPlayType(mimeType);
  console.log(`Browser support for ${format} (${mimeType}): ${canPlay}`);
  return canPlay !== '';
};

// Function to find the first available video format
const findAvailableVideo = async (baseName: string): Promise<string | null> => {
  const formats = ['mp4', 'webm', 'mov'];

  for (const format of formats) {
    const src = `/videos/${baseName}.${format}`;

    // Check browser support first
    if (!canPlayFormat(format)) {
      console.warn(`<PERSON>rowser doesn't support ${format} format, skipping ${src}`);
      continue;
    }

    try {
      console.log(`Checking for ${src}...`);
      const response = await fetch(src, { method: 'HEAD' });
      console.log(`Response for ${src}: ${response.status} ${response.statusText}`);

      // Log response headers for debugging
      console.log(`Content-Type: ${response.headers.get('content-type')}`);
      console.log(`Content-Length: ${response.headers.get('content-length')}`);

      if (response.ok) {
        console.log(`✅ Found and verified: ${src}`);
        return src;
      } else {
        console.log(`❌ File not found: ${src} (${response.status})`);
      }
    } catch (error) {
      console.error(`❌ Error checking ${src}:`, error);
    }
  }

  console.warn(`❌ No video found for ${baseName} in any supported format`);
  return null;
};

// Function to get video duration with better error handling
const getVideoDuration = (src: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';
    video.crossOrigin = 'anonymous';

    let timeoutId: NodeJS.Timeout;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      video.removeEventListener('loadedmetadata', onLoadedMetadata);
      video.removeEventListener('error', onError);
      video.removeEventListener('canplay', onCanPlay);
    };

    const onLoadedMetadata = () => {
      const duration = video.duration;
      console.log(`✅ Video ${src} duration: ${duration} seconds`);
      cleanup();
      resolve(duration);
    };

    const onCanPlay = () => {
      if (video.duration && !isNaN(video.duration) && video.duration > 0) {
        const duration = video.duration;
        console.log(`✅ Video ${src} duration (via canplay): ${duration} seconds`);
        cleanup();
        resolve(duration);
      }
    };

    const onError = (event: Event) => {
      console.error(`❌ Failed to load video metadata for ${src}:`, event);
      if (video.error) {
        console.error(`❌ Video error code: ${video.error.code}`);
        console.error(`❌ Video error message: ${video.error.message}`);
        const errorMessages = {
          1: 'MEDIA_ERR_ABORTED - The video download was aborted',
          2: 'MEDIA_ERR_NETWORK - A network error occurred',
          3: 'MEDIA_ERR_DECODE - The video is corrupted or not supported',
          4: 'MEDIA_ERR_SRC_NOT_SUPPORTED - The video format is not supported'
        };
        console.error(`❌ Error details: ${errorMessages[video.error.code as keyof typeof errorMessages] || 'Unknown error'}`);
      }
      cleanup();
      reject(new Error(`Failed to load video: ${src}`));
    };

    // Set up event listeners
    video.addEventListener('loadedmetadata', onLoadedMetadata);
    video.addEventListener('error', onError);
    video.addEventListener('canplay', onCanPlay);

    // Set timeout to prevent hanging
    timeoutId = setTimeout(() => {
      console.error(`❌ Timeout loading video metadata for ${src}`);
      cleanup();
      reject(new Error(`Timeout loading video: ${src}`));
    }, 10000); // 10 second timeout

    console.log(`🔄 Loading metadata for ${src}...`);
    video.src = src;
  });
};

// Initial video clips - will be populated with actual available videos
const initialVideoClips = [
  { baseName: 'clip1', name: 'Clip 1' },
  { baseName: 'clip2', name: 'Clip 2' },
  { baseName: 'clip3', name: 'Clip 3' }
];

interface VideoClipData {
  src: string;
  durationInFrames: number;
  durationInSeconds: number;
  name: string;
  format: string;
}

export const VideoStitching: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const [videoClips, setVideoClips] = useState<VideoClipData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('VideoStitching component mounted');
    console.log('Loading available video formats...');

    const loadAvailableVideos = async () => {
      setLoading(true);

      const loadedClips: VideoClipData[] = [];

      for (const clip of initialVideoClips) {
        const availableVideo = await findAvailableVideo(clip.baseName);

        if (availableVideo) {
          try {
            const durationInSeconds = await getVideoDuration(availableVideo);
            const durationInFrames = Math.round(durationInSeconds * fps);
            const format = availableVideo.split('.').pop() || 'unknown';

            loadedClips.push({
              src: availableVideo,
              durationInSeconds,
              durationInFrames,
              name: clip.name,
              format: format.toUpperCase()
            });

            console.log(`Loaded ${clip.name}: ${availableVideo} (${durationInSeconds.toFixed(1)}s, ${format.toUpperCase()})`);
          } catch (error) {
            console.error(`Failed to load duration for ${availableVideo}:`, error);
          }
        } else {
          console.warn(`No video file found for ${clip.baseName}`);
        }
      }

      setVideoClips(loadedClips);
      setLoading(false);
      console.log('All available videos loaded:', loadedClips);
    };

    loadAvailableVideos();
  }, [fps]);

  useEffect(() => {
    if (frame % 30 === 0) { // Log every second
      console.log('Current frame:', frame);
    }
  }, [frame]);

  // Calculate total duration
  const totalDurationInFrames = videoClips.reduce((total, clip) => total + clip.durationInFrames, 0);
  const totalDurationInSeconds = videoClips.reduce((total, clip) => total + clip.durationInSeconds, 0);

  // Show loading state
  if (loading) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        backgroundColor: 'black',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '16px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div>🎬 Scanning for video files...</div>
          <div style={{ fontSize: '12px', marginTop: '10px', opacity: 0.7 }}>
            Checking MP4, WebM, and MOV formats
          </div>
        </div>
      </div>
    );
  }

  // Show message if no videos found
  if (videoClips.length === 0) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        backgroundColor: 'black',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '16px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div>❌ No video files found</div>
          <div style={{ fontSize: '12px', marginTop: '10px', opacity: 0.7 }}>
            Please add clip1, clip2, or clip3 in MP4, WebM, or MOV format to /public/videos/
          </div>
        </div>
      </div>
    );
  }

  let currentFrame = 0;

  return (
    <div style={{
      flex: 1,
      backgroundColor: 'black',
      position: 'relative',
      width: '100%',
      height: '100%'
    }}>
      {videoClips.map((clip, index) => {
        const sequence = (
          <Sequence
            key={index}
            from={currentFrame}
            durationInFrames={clip.durationInFrames}
          >
            <VideoClip src={clip.src} />
          </Sequence>
        );

        console.log(`Sequence ${index}: from ${currentFrame} to ${currentFrame + clip.durationInFrames} (${clip.durationInSeconds.toFixed(1)}s, ${clip.format})`);
        currentFrame += clip.durationInFrames;
        return sequence;
      })}

      {/* Enhanced debug overlay */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        color: 'white',
        backgroundColor: 'rgba(0,0,0,0.8)',
        padding: '8px 12px',
        borderRadius: '6px',
        fontSize: '12px',
        zIndex: 1000,
        fontFamily: 'monospace'
      }}>
        <div>Frame: {frame} / {totalDurationInFrames}</div>
        <div>Time: {(frame / fps).toFixed(1)}s / {totalDurationInSeconds.toFixed(1)}s</div>
        <div style={{ marginTop: '4px', fontSize: '10px', opacity: 0.8 }}>
          {videoClips.map((clip, i) => (
            <div key={i}>
              {clip.name}: {clip.durationInSeconds.toFixed(1)}s ({clip.format})
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

