import { Video } from 'remotion';
import { useState } from 'react';

interface VideoClipProps {
  src: string;
  startFrom?: number;
  endAt?: number;
}

export const VideoClip: React.FC<VideoClipProps> = ({
  src,
  startFrom = 0,
  endAt
}) => {
  const [error, setError] = useState<string | null>(null);
  const [loaded, setLoaded] = useState(false);

  const handleError = (event: any) => {
    const errorMessage = `Failed to load video: ${src}`;
    setError(errorMessage);
    console.error(`❌ VideoClip error for ${src}:`, event);

    // Log additional error details if available
    if (event.target && event.target.error) {
      console.error(`❌ Video error code: ${event.target.error.code}`);
      console.error(`❌ Video error message: ${event.target.error.message}`);
    }
  };

  const handleLoad = () => {
    setLoaded(true);
    console.log(`✅ VideoClip successfully loaded: ${src}`);
  };

  const handleLoadStart = () => {
    console.log(`🔄 VideoClip load started: ${src}`);
  };

  const handleCanPlay = () => {
    if (!loaded) {
      setLoaded(true);
      console.log(`✅ VideoClip can play: ${src}`);
    }
  };

  if (error) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f0f0f0',
        color: '#666',
        fontSize: '14px',
        textAlign: 'center',
        padding: '20px'
      }}>
        <div>
          <div>❌ Video Load Error</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>
            {src}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {!loaded && (
        <div style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f0f0f0',
          color: '#666',
          fontSize: '14px'
        }}>
          Loading video: {src}
        </div>
      )}
      <Video
        src={src}
        startFrom={startFrom}
        endAt={endAt}
        onError={handleError}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: loaded ? 1 : 0.1
        }}
        muted
        playsInline
        onLoadedData={handleLoad}
        onLoadStart={handleLoadStart}
        onCanPlay={handleCanPlay}
      />
    </>
  );
};
